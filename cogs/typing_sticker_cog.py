import discord
from discord.ext import commands

class TypingStickerCog(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.enabled = True
        self.target_user_id = 1141746562922459136

    @commands.Cog.listener()
    async def on_typing(self, channel: discord.TextChannel, user: discord.User, when: discord.Message) -> None:
        if not self.enabled:
            return
        if user.id == self.target_user_id:
            sticker_id = 1385493486509228032
            try:
                sticker = await self.bot.fetch_sticker(sticker_id)
                await channel.send(f'<@{user.id}>', stickers=[sticker])
            except discord.NotFound:
                await channel.send(f"Could not find sticker with ID {sticker_id}")
            except discord.Forbidden:
                await channel.send("I don't have permission to send messages or stickers in this channel.")


    @commands.command()
    @commands.is_owner()
    async def toggletypingsticker(self, ctx):
        """Toggles the typing sticker cog on or off."""
        self.enabled = not self.enabled
        status = "enabled" if self.enabled else "disabled"
        await ctx.send(f"Typing sticker cog is now {status}.")

async def setup(bot):
    await bot.add_cog(TypingStickerCog(bot))