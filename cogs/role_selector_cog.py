import discord
from discord.ext import commands
from discord import app_commands
from discord.ui import View, Select, Button, Modal, TextInput
import json
import os
from typing import List, Dict, Optional, Set, Tuple, Union
import asyncio
import re
import uuid

# Database and Pydantic Models
from api_service.api_server import db
from api_service.api_models import (
    RoleOption,
    RoleCategoryPreset,
    GuildRole,
    GuildRoleCategoryConfig,
    UserCustomColorRole,
)

async def is_owner_check(interaction: discord.Interaction) -> bool:
    """Checks if the interacting user is the bot owner."""
    return interaction.user.id == interaction.client.owner_id


# For color name validation
try:
    from matplotlib.colors import is_color_like, to_rgb, XKCD_COLORS
except ImportError:
    XKCD_COLORS = {}  # Fallback if matplotlib is not installed

    def is_color_like(c):  # Basic fallback
        if isinstance(c, str):
            return c.startswith("#") and len(c) in [4, 7]
        return False

    def to_rgb(c):  # Basic fallback
        if isinstance(c, str) and c.startswith("#"):
            hex_color = c.lstrip("#")
            if len(hex_color) == 3:
                return tuple(int(hex_color[i] * 2, 16) / 255.0 for i in range(3))
            if len(hex_color) == 6:
                return tuple(
                    int(hex_color[i : i + 2], 16) / 255.0 for i in range(0, 6, 2)
                )
        return (0, 0, 0)  # Default black


# --- Color Parsing Helper ---
def _parse_color_input(color_input: str) -> Optional[discord.Color]:
    """Parses a color input string (hex, rgb, or name) into a discord.Color object."""
    color_input = color_input.strip()

    # Try hex
    hex_match = re.fullmatch(r"#?([0-9a-fA-F]{3}|[0-9a-fA-F]{6})", color_input)
    if hex_match:
        hex_val = hex_match.group(1)
        if len(hex_val) == 3:
            hex_val = "".join([c * 2 for c in hex_val])
        try:
            return discord.Color(int(hex_val, 16))
        except ValueError:
            pass  # Should not happen with regex match

    # Try RGB: "r, g, b" or "(r, g, b)"
    rgb_match = re.fullmatch(
        r"\(?\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*\)?", color_input
    )
    if rgb_match:
        try:
            r, g, b = [int(x) for x in rgb_match.groups()]
            if all(0 <= val <= 255 for val in (r, g, b)):
                return discord.Color.from_rgb(r, g, b)
        except ValueError:
            pass

    # Try English color name (matplotlib XKCD_COLORS)
    if XKCD_COLORS:  # Check if matplotlib was imported
        normalized_input = color_input.lower().replace(" ", "")
        # Check against normalized keys
        for xkcd_name_key, xkcd_hex_val in XKCD_COLORS.items():
            if xkcd_name_key.lower().replace(" ", "") == normalized_input:
                try:
                    # to_rgb for xkcd colors usually returns (r,g,b) float tuple
                    rgb_float = to_rgb(xkcd_hex_val)
                    return discord.Color.from_rgb(
                        int(rgb_float[0] * 255),
                        int(rgb_float[1] * 255),
                        int(rgb_float[2] * 255),
                    )
                except Exception:
                    pass  # Matplotlib color conversion failed
                break
    # Fallback for very common color names if matplotlib is not available
    elif color_input.lower() in {
        "red": (255, 0, 0),
        "green": (0, 255, 0),
        "blue": (0, 0, 255),
        "yellow": (255, 255, 0),
        "purple": (128, 0, 128),
        "orange": (255, 165, 0),
        "pink": (255, 192, 203),
        "black": (0, 0, 0),
        "white": (255, 255, 255),
    }:
        r, g, b = {
            "red": (255, 0, 0),
            "green": (0, 255, 0),
            "blue": (0, 0, 255),
            "yellow": (255, 255, 0),
            "purple": (128, 0, 128),
            "orange": (255, 165, 0),
            "pink": (255, 192, 203),
            "black": (0, 0, 0),
            "white": (255, 255, 255),
        }[color_input.lower()]
        return discord.Color.from_rgb(r, g, b)
    return None


# --- Custom Color Modal ---
class CustomColorModal(Modal, title="Set Your Custom Role Color"):
    color_input = TextInput(
        label="Color (Hex, RGB, or Name)",
        placeholder="#RRGGBB, 255,0,128, or 'sky blue'",
        style=discord.TextStyle.short,
        required=True,
        max_length=100,
    )

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True, thinking=True)
        guild = interaction.guild
        member = interaction.user
        if not guild or not isinstance(member, discord.Member):
            await interaction.followup.send(
                "This can only be used in a server.", ephemeral=True
            )
            return

        parsed_color = _parse_color_input(self.color_input.value)
        if not parsed_color:
            await interaction.followup.send(
                f"Could not understand the color '{self.color_input.value}'.\n"
                "Please use a hex code (e.g., `#FF0000`), RGB values (e.g., `255,0,0`), "
                "or a known color name (e.g., 'red', 'sky blue').",
                ephemeral=True,
            )
            return

        custom_role_name = f"User Color - {member.id}"
        existing_user_color_role_db = db.get_user_custom_color_role(
            str(guild.id), str(member.id)
        )

        role_to_update: Optional[discord.Role] = None

        if existing_user_color_role_db:
            role_to_update = guild.get_role(int(existing_user_color_role_db.role_id))
            if not role_to_update:
                db.delete_user_custom_color_role(str(guild.id), str(member.id))
                existing_user_color_role_db = None
            elif (
                role_to_update.name != custom_role_name
            ):  # Name mismatch, could be manually changed
                try:  # Try to rename it back
                    await role_to_update.edit(
                        name=custom_role_name,
                        reason="Standardizing custom color role name",
                    )
                except discord.Forbidden:
                    await interaction.followup.send(
                        "I couldn't standardize your existing color role name. Please check my permissions.",
                        ephemeral=True,
                    )
                    # Potentially fall through to create a new one if renaming fails and old one is problematic
                except discord.HTTPException:
                    pass  # Non-critical error, proceed with color update

        if not role_to_update:
            for r in guild.roles:  # Check if a role with the target name already exists
                if r.name == custom_role_name:
                    role_to_update = r
                    break
            if not role_to_update:
                try:
                    role_to_update = await guild.create_role(
                        name=custom_role_name,
                        color=parsed_color,  # Set initial color
                        reason=f"Custom color role for {member.display_name}",
                    )
                except discord.Forbidden:
                    await interaction.followup.send(
                        "I don't have permission to create roles.", ephemeral=True
                    )
                    return
                except discord.HTTPException as e:
                    await interaction.followup.send(
                        f"Failed to create role: {e}", ephemeral=True
                    )
                    return

        if not role_to_update:
            await interaction.followup.send(
                "Failed to obtain a role to update.", ephemeral=True
            )
            return

        if role_to_update.color != parsed_color:
            try:
                await role_to_update.edit(
                    color=parsed_color, reason=f"Color update for {member.display_name}"
                )
            except discord.Forbidden:
                await interaction.followup.send(
                    "I don't have permission to edit the role color.", ephemeral=True
                )
                return
            except discord.HTTPException as e:
                await interaction.followup.send(
                    f"Failed to update role color: {e}", ephemeral=True
                )
                return

        roles_to_add_to_member = []
        if role_to_update.id not in [r.id for r in member.roles]:
            roles_to_add_to_member.append(role_to_update)

        roles_to_remove_from_member = [
            r
            for r in member.roles
            if r.name.startswith("User Color - ") and r.id != role_to_update.id
        ]

        try:
            if roles_to_remove_from_member:
                await member.remove_roles(
                    *roles_to_remove_from_member,
                    reason="Cleaning up old custom color roles",
                )
            if roles_to_add_to_member:
                await member.add_roles(
                    *roles_to_add_to_member, reason="Applied custom color role"
                )
        except discord.Forbidden:
            await interaction.followup.send(
                "I don't have permission to assign roles.", ephemeral=True
            )
            return
        except discord.HTTPException as e:
            await interaction.followup.send(
                f"Failed to assign role: {e}", ephemeral=True
            )
            return

        user_color_role_data = UserCustomColorRole(
            user_id=str(member.id),
            guild_id=str(guild.id),
            role_id=str(role_to_update.id),
            hex_color=f"#{parsed_color.value:06x}",
        )
        db.save_user_custom_color_role(user_color_role_data)

        # New logic: Remove roles from "Colors" preset if custom color is set
        removed_preset_color_roles_names = []
        # Ensure guild is not None, though it should be from earlier checks (line 103)
        if guild and isinstance(member, discord.Member):  # member check for safety
            guild_role_categories = db.get_guild_role_category_configs(str(guild.id))
            colors_preset_role_ids_to_remove = set()

            for cat_config in guild_role_categories:
                # "Colors" preset ID is 'default_colors'
                if cat_config.is_preset and cat_config.preset_id == "default_colors":
                    for role_option in cat_config.roles:
                        colors_preset_role_ids_to_remove.add(int(role_option.role_id))
                    break  # Found the Colors preset for this guild

            if colors_preset_role_ids_to_remove:
                roles_to_actually_remove_from_member = []
                for member_role in member.roles:
                    if member_role.id in colors_preset_role_ids_to_remove:
                        roles_to_actually_remove_from_member.append(member_role)

                if roles_to_actually_remove_from_member:
                    try:
                        await member.remove_roles(
                            *roles_to_actually_remove_from_member,
                            reason="User set a custom color, removing preset color role(s).",
                        )
                        removed_preset_color_roles_names = [
                            r.name for r in roles_to_actually_remove_from_member
                        ]
                    except discord.Forbidden:
                        await interaction.followup.send(
                            "I tried to remove your preset color role(s) but lack permissions.",
                            ephemeral=True,
                        )
                    except discord.HTTPException as e:
                        await interaction.followup.send(
                            f"Failed to remove your preset color role(s): {e}",
                            ephemeral=True,
                        )

        feedback_message = (
            f"Your custom role color has been set to {user_color_role_data.hex_color}!"
        )
        if removed_preset_color_roles_names:
            feedback_message += f"\nRemoved preset color role(s): {', '.join(removed_preset_color_roles_names)}."
        await interaction.followup.send(feedback_message, ephemeral=True)

    async def on_error(self, interaction: discord.Interaction, error: Exception):
        await interaction.followup.send(f"An error occurred: {error}", ephemeral=True)
        print(f"Error in CustomColorModal: {error}")


# --- View for the Custom Color Button ---
class CustomColorButtonView(View):
    def __init__(self):
        super().__init__(timeout=None)

    @discord.ui.button(
        label="Set Custom Role Color",
        style=discord.ButtonStyle.primary,
        custom_id="persistent_set_custom_color_button",
    )
    async def set_color_button_callback(
        self, interaction: discord.Interaction, button: discord.ui.Button
    ):
        modal = CustomColorModal()
        await interaction.response.send_modal(modal)


# --- Persistent View Definition ---
class RoleSelectorView(View):
    def __init__(
        self, guild_id: int, category_config: GuildRoleCategoryConfig, bot_instance
    ):
        super().__init__(timeout=None)
        self.guild_id = guild_id
        self.category_config = category_config
        self.bot = bot_instance
        self.category_role_ids: Set[int] = {
            int(role.role_id) for role in category_config.roles
        }
        self.custom_id = (
            f"persistent_role_select_view_{guild_id}_{category_config.category_id}"
        )
        self.select_chunk_map: Dict[str, Set[int]] = {}

        category_display_roles: List[GuildRole] = category_config.roles
        self.role_chunks = [
            category_display_roles[i : i + 25]
            for i in range(0, len(category_display_roles), 25)
        ]
        num_chunks = len(self.role_chunks)
        total_max_values = min(
            category_config.max_selectable, len(category_display_roles)
        )
        actual_min_values = 0

        for i, chunk in enumerate(self.role_chunks):
            options = [
                discord.SelectOption(
                    label=role.name,
                    value=str(role.role_id),
                    emoji=role.emoji if role.emoji else None,
                )
                for role in chunk
            ]
            chunk_role_ids = {int(role.role_id) for role in chunk}
            if not options:
                continue
            chunk_max_values = min(total_max_values, len(options))
            placeholder = f"Select {category_config.name} role(s)..."
            if num_chunks > 1:
                placeholder = (
                    f"Select {category_config.name} role(s) ({i+1}/{num_chunks})..."
                )
            select_custom_id = (
                f"role_select_dropdown_{guild_id}_{category_config.category_id}_{i}"
            )
            self.select_chunk_map[select_custom_id] = chunk_role_ids
            select_component = Select(
                placeholder=placeholder,
                min_values=actual_min_values,
                max_values=chunk_max_values,
                options=options,
                custom_id=select_custom_id,
            )
            select_component.callback = self.select_callback
            self.add_item(select_component)

    async def select_callback(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True, thinking=True)
        member = interaction.user
        guild = interaction.guild
        if not isinstance(member, discord.Member) or not guild:
            await interaction.followup.send(
                "This interaction must be used within a server.", ephemeral=True
            )
            return
        if guild.id != self.guild_id:
            await interaction.followup.send(
                "This role selector is not for this server.", ephemeral=True
            )
            return

        interacted_custom_id = interaction.data["custom_id"]
        interacted_chunk_role_ids: Set[int] = self.select_chunk_map.get(
            interacted_custom_id, set()
        )
        if not interacted_chunk_role_ids:
            for component in self.children:
                if (
                    isinstance(component, Select)
                    and component.custom_id == interacted_custom_id
                ):
                    interacted_chunk_role_ids = {
                        int(opt.value) for opt in component.options
                    }
                    break
            if not interacted_chunk_role_ids:
                await interaction.followup.send(
                    "An internal error occurred identifying roles for this dropdown.",
                    ephemeral=True,
                )
                return

        selected_values = interaction.data.get("values", [])
        selected_role_ids_from_interaction = {int(value) for value in selected_values}
        member_category_role_ids = {
            role.id for role in member.roles if role.id in self.category_role_ids
        }
        roles_to_add_ids = selected_role_ids_from_interaction - member_category_role_ids
        member_roles_in_interacted_chunk = member_category_role_ids.intersection(
            interacted_chunk_role_ids
        )
        roles_to_remove_ids = (
            member_roles_in_interacted_chunk - selected_role_ids_from_interaction
        )

        if self.category_config.max_selectable == 1 and roles_to_add_ids:
            if len(roles_to_add_ids) > 1:
                await interaction.followup.send(
                    f"Error: Cannot select multiple roles for '{self.category_config.name}'.",
                    ephemeral=True,
                )
                return
            role_to_add_id = list(roles_to_add_ids)[0]
            other_member_roles_in_category = member_category_role_ids - {role_to_add_id}
            roles_to_remove_ids.update(other_member_roles_in_category)
            roles_to_add_ids = {role_to_add_id}

        roles_to_add = {
            guild.get_role(role_id)
            for role_id in roles_to_add_ids
            if guild.get_role(role_id)
        }
        roles_to_remove = {
            guild.get_role(role_id)
            for role_id in roles_to_remove_ids
            if guild.get_role(role_id)
        }

        # New: Handle mutually exclusive categories
        if roles_to_add and hasattr(self.category_config, 'mutually_exclusive_with') and self.category_config.mutually_exclusive_with:
            all_guild_configs = db.get_guild_role_category_configs(str(guild.id))
            exclusive_category_ids = set(self.category_config.mutually_exclusive_with)
            
            exclusive_role_ids = set()
            for other_config in all_guild_configs:
                if other_config.category_id in exclusive_category_ids:
                    for role_option in other_config.roles:
                        exclusive_role_ids.add(int(role_option.role_id))
            
            for member_role in member.roles:
                if member_role.id in exclusive_role_ids:
                    roles_to_remove.add(member_role)

        added_names, removed_names, error_messages = [], [], []
        removed_custom_color_feedback = ""  # Initialize here

        # New logic: If adding a "Colors" preset role, remove custom color role
        # The preset_id for "Colors" is 'default_colors'
        is_colors_preset_category = (
            self.category_config.is_preset
            and self.category_config.preset_id == "default_colors"
        )

        # A color from the "Colors" preset is being added (roles_to_add is not empty)
        if is_colors_preset_category and roles_to_add:
            # Ensure member and guild are valid (they should be from earlier checks in lines 262-267)
            if isinstance(member, discord.Member) and guild:
                existing_user_custom_color_db = db.get_user_custom_color_role(
                    str(guild.id), str(member.id)
                )
                if existing_user_custom_color_db:
                    custom_color_role_to_remove = guild.get_role(
                        int(existing_user_custom_color_db.role_id)
                    )
                    if custom_color_role_to_remove:
                        try:
                            await member.remove_roles(
                                custom_color_role_to_remove,
                                reason="User selected a preset color, removing custom color role.",
                            )
                            db.delete_user_custom_color_role(
                                str(guild.id), str(member.id)
                            )  # Delete from DB
                            removed_custom_color_feedback = f"\n- Removed custom color role '{custom_color_role_to_remove.name}'."
                        except discord.Forbidden:
                            error_messages.append(
                                "Could not remove your custom color role (permissions)."
                            )
                        except discord.HTTPException as e:
                            error_messages.append(
                                f"Error removing custom color role: {e}"
                            )
                    else:  # Role not found in guild, but was in DB. Clean up DB.
                        db.delete_user_custom_color_role(str(guild.id), str(member.id))
                        removed_custom_color_feedback = "\n- Your previous custom color role was not found in the server and has been cleared from my records."

        try:
            if roles_to_remove:
                await member.remove_roles(
                    *roles_to_remove,
                    reason=f"Deselected/changed via {self.category_config.name} role selector ({interacted_custom_id})",
                )
                removed_names = [r.name for r in roles_to_remove if r]
            if roles_to_add:
                await member.add_roles(
                    *roles_to_add,
                    reason=f"Selected via {self.category_config.name} role selector ({interacted_custom_id})",
                )
                added_names = [r.name for r in roles_to_add if r]

            feedback = "Your roles have been updated!"
            if added_names:
                feedback += f"\n+ Added: {', '.join(added_names)}"
            if removed_names:
                feedback += f"\n- Removed: {', '.join(removed_names)}"
            feedback += removed_custom_color_feedback  # Add the custom color removal feedback here

            # Adjusted condition for "no changes" message
            # Ensure removed_custom_color_feedback is considered. If it has content, changes were made.
            if (
                not added_names
                and not removed_names
                and not removed_custom_color_feedback.strip()
            ):
                if selected_values:
                    feedback = (
                        "No changes needed for the roles selected in this dropdown."
                    )
                else:
                    feedback = (
                        "No roles selected in this dropdown."
                        if not member_roles_in_interacted_chunk
                        else "Roles deselected from this dropdown."
                    )
            await interaction.followup.send(feedback, ephemeral=True)
        except discord.Forbidden:
            error_messages.append("I don't have permission to manage roles.")
        except discord.HTTPException as e:
            error_messages.append(f"An error occurred: {e}")
        except Exception as e:
            error_messages.append(f"Unexpected error: {e}")
            print(f"Error in role selector: {e}")
        if error_messages:
            await interaction.followup.send("\n".join(error_messages), ephemeral=True)

# --- Constants ---
DEFAULT_ROLE_COLOR = discord.Color.default()

# --- Admin Management UI ---

# Forward declaration for type hinting
class RoleSelectorCog:
    pass

class ConfirmView(View):
    """A simple view for confirming or cancelling an action."""
    def __init__(self, author_id: int):
        super().__init__(timeout=60.0)
        self.value = None
        self.author_id = author_id

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        if interaction.user.id != self.author_id:
            await interaction.response.send_message("This confirmation is not for you.", ephemeral=True)
            return False
        return True

    @discord.ui.button(label="Confirm", style=discord.ButtonStyle.danger)
    async def confirm_button(self, interaction: discord.Interaction, button: Button):
        self.value = True
        self.stop()
        await interaction.response.defer()

    @discord.ui.button(label="Cancel", style=discord.ButtonStyle.grey)
    async def cancel_button(self, interaction: discord.Interaction, button: Button):
        self.value = False
        self.stop()
        await interaction.response.defer()

class CategoryModal(Modal):
    """Modal for creating or editing a role category."""
    def __init__(self, title: str, config: Optional[GuildRoleCategoryConfig] = None):
        super().__init__(title=title, timeout=300)
        self.name = TextInput(
            label="Category Name",
            placeholder="e.g., 'Color Roles', 'Region Roles'",
            default=config.name if config else "",
            max_length=100,
            required=True,
        )
        self.description = TextInput(
            label="Description",
            style=discord.TextStyle.paragraph,
            placeholder="A short description shown above the role selector.",
            default=config.description if config else "",
            max_length=1024,
            required=False,
        )
        self.max_selectable = TextInput(
            label="Max Selectable Roles",
            placeholder="e.g., 1 for colors, 5 for interests",
            default=str(config.max_selectable) if config else "1",
            max_length=2,
            required=True,
        )
        self.add_item(self.name)
        self.add_item(self.description)
        self.add_item(self.max_selectable)

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True)

class AddRoleModal(Modal, title="Add Role to Category"):
    def __init__(self):
        super().__init__()
        self.role_input = TextInput(
            label="Role Name or ID",
            placeholder="Enter the exact name or ID of the role.",
            required=True
        )
        self.emoji_input = TextInput(
            label="Emoji (Optional)",
            placeholder="Enter a single emoji for this role.",
            required=False,
            max_length=10
        )
        self.add_item(self.role_input)
        self.add_item(self.emoji_input)

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True)

# NOTE TO DEVS: This requires 'mutually_exclusive_with: List[str] = Field(default_factory=list)'
# to be added to the GuildRoleCategoryConfig model in api_service/api_models.py
class MutexSelect(discord.ui.Select):
    """A select menu to manage mutually exclusive categories."""
    def __init__(self, cog: 'RoleSelectorCog', config: GuildRoleCategoryConfig):
        self.cog = cog
        self.config = config
        # Ensure the attribute exists.
        if not hasattr(config, 'mutually_exclusive_with') or config.mutually_exclusive_with is None:
            config.mutually_exclusive_with = []

        all_configs = db.get_guild_role_category_configs(config.guild_id)
        options = [
            discord.SelectOption(
                label=c.name,
                value=c.category_id,
                default=c.category_id in config.mutually_exclusive_with
            )
            for c in all_configs if c.category_id != config.category_id
        ]
        super().__init__(
            placeholder="Select mutually exclusive categories...",
            options=options,
            custom_id=f"mutex_select_{config.category_id}",
            max_values=len(options) if options else 1,
            min_values=0,
            disabled=not options,
            row=4 # Place it on the last row
        )

    async def callback(self, interaction: discord.Interaction):
        await interaction.response.defer(ephemeral=True)
        selected_ids = set(self.values)
        my_id = self.config.category_id

        # Update current config
        self.config.mutually_exclusive_with = list(selected_ids)
        db.save_guild_role_category_config(self.config)

        # Update other configs for symmetry
        all_configs = db.get_guild_role_category_configs(self.config.guild_id)
        for other_config in all_configs:
            if other_config.category_id == my_id:
                continue

            # Ensure attribute exists on other configs
            if not hasattr(other_config, 'mutually_exclusive_with') or other_config.mutually_exclusive_with is None:
                other_config.mutually_exclusive_with = []
            
            other_exclusive_set = set(other_config.mutually_exclusive_with)

            # If this config is now exclusive with other_config
            if other_config.category_id in selected_ids:
                if my_id not in other_exclusive_set:
                    other_config.mutually_exclusive_with.append(my_id)
                    db.save_guild_role_category_config(other_config)
            # If this config is no longer exclusive with other_config
            else:
                if my_id in other_exclusive_set:
                    other_config.mutually_exclusive_with.remove(my_id)
                    db.save_guild_role_category_config(other_config)
        
        await interaction.followup.send("Mutual exclusivity settings updated.", ephemeral=True)


class CategoryManagementView(View):
    """View for managing a single role category."""
    def __init__(self, cog: 'RoleSelectorCog', interaction: discord.Interaction, category_id: str):
        super().__init__(timeout=300)
        self.cog = cog
        self.author_id = interaction.user.id
        self.guild = interaction.guild
        self.category_id = category_id
        self.message: Optional[discord.Message] = None
        self.selected_role_to_remove: Optional[str] = None
        self.update_view()

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        if interaction.user.id != self.author_id:
            await interaction.response.send_message("This is not for you!", ephemeral=True)
            return False
        return True

    def get_config(self) -> Optional[GuildRoleCategoryConfig]:
        return self.cog._get_guild_category_config(self.guild.id, self.category_id)

    def update_view(self):
        self.clear_items()
        config = self.get_config()
        if not config:
            self.add_item(Button(label="Category not found. Go back.", custom_id="back_to_dashboard"))
            return

        # Role selection dropdown
        if config.roles:
            options = [
                discord.SelectOption(label=r.name, value=r.role_id, emoji=r.emoji)
                for r in config.roles
            ]
            self.add_item(Select(placeholder="Select a role to remove...", options=options, custom_id="select_role_to_remove"))

        # Action buttons
        self.add_item(Button(label="Edit Settings", style=discord.ButtonStyle.primary, custom_id="edit_category"))
        if not config.is_preset:
            self.add_item(Button(label="Add Role", style=discord.ButtonStyle.success, custom_id="add_role"))
            self.add_item(Button(label="Remove Selected Role", style=discord.ButtonStyle.danger, custom_id="remove_role", disabled=self.selected_role_to_remove is None))
        self.add_item(Button(label="Post/Update Selector", style=discord.ButtonStyle.secondary, custom_id="post_selector"))
        self.add_item(Button(label="Delete Category", style=discord.ButtonStyle.danger, custom_id="delete_category"))
        self.add_item(Button(label="Back to Dashboard", style=discord.ButtonStyle.grey, custom_id="back_to_dashboard"))
        
        # Add the new MutexSelect if the config is not a preset
        if not config.is_preset:
            self.add_item(MutexSelect(self.cog, config))

        # Set callbacks
        for item in self.children:
            if hasattr(item, 'custom_id'):
                item.callback = self.dispatch_callback

    async def dispatch_callback(self, interaction: discord.Interaction):
        custom_id = interaction.data['custom_id']
        handler = getattr(self, f"handle_{custom_id}", None)
        if handler:
            await handler(interaction)
        else:
            await interaction.response.defer()

    async def handle_back_to_dashboard(self, interaction: discord.Interaction):
        await self.cog.show_management_dashboard(interaction, self.message)

    async def handle_edit_category(self, interaction: discord.Interaction):
        config = self.get_config()
        if not config:
            await interaction.response.send_message("Category not found.", ephemeral=True)
            return

        modal = CategoryModal(title=f"Editing '{config.name}'", config=config)
        await interaction.response.send_modal(modal)
        timed_out = await modal.wait()

        if not timed_out:
            try:
                max_selectable = int(modal.max_selectable.value)
                if not (1 <= max_selectable <= 25):
                    raise ValueError("Max selectable must be between 1 and 25.")
            except ValueError as e:
                await interaction.followup.send(f"Invalid input: {e}", ephemeral=True)
                return

            config.name = modal.name.value
            config.description = modal.description.value
            config.max_selectable = max_selectable
            db.save_guild_role_category_config(config)
            await interaction.followup.send(f"Category '{config.name}' updated.", ephemeral=True)
            await self.cog.show_category_manager(interaction, self.category_id, self.message) # Refresh

    async def handle_add_role(self, interaction: discord.Interaction):
        config = self.get_config()
        if not config:
            await interaction.response.send_message("Category not found.", ephemeral=True)
            return
        
        modal = AddRoleModal()
        await interaction.response.send_modal(modal)
        timed_out = await modal.wait()

        if timed_out:
            return

        role_input = modal.role_input.value
        role: Optional[discord.Role] = None
        try:
            role = await commands.RoleConverter().convert(interaction, role_input)
        except commands.RoleNotFound:
            await interaction.followup.send(f"Could not find a role matching '{role_input}'.", ephemeral=True)
            return
        
        if any(r.role_id == str(role.id) for r in config.roles):
            await interaction.followup.send(f"Role '{role.name}' is already in this category.", ephemeral=True)
            return

        config.roles.append(GuildRole(role_id=str(role.id), name=role.name, emoji=modal.emoji_input.value or None))
        db.save_guild_role_category_config(config)
        await interaction.followup.send(f"Role '{role.name}' added to '{config.name}'.", ephemeral=True)
        await self.cog.show_category_manager(interaction, self.category_id, self.message)

    async def handle_select_role_to_remove(self, interaction: discord.Interaction):
        self.selected_role_to_remove = interaction.data['values'][0]
        self.update_view()
        await interaction.response.edit_message(view=self)

    async def handle_remove_role(self, interaction: discord.Interaction):
        config = self.get_config()
        if not config or not self.selected_role_to_remove:
            await interaction.response.send_message("No role selected to remove. Please select one from the dropdown first.", ephemeral=True)
            return

        role_name = ""
        initial_len = len(config.roles)
        for r in config.roles:
            if r.role_id == self.selected_role_to_remove:
                role_name = r.name
                break
        
        config.roles = [r for r in config.roles if r.role_id != self.selected_role_to_remove]
        
        if len(config.roles) < initial_len:
            db.save_guild_role_category_config(config)
            self.selected_role_to_remove = None # Reset selection
            await interaction.response.send_message(f"Role '{role_name}' removed from '{config.name}'.", ephemeral=True)
            await self.cog.show_category_manager(interaction, self.category_id, self.message)
        else:
            await interaction.response.send_message("Could not find the selected role to remove.", ephemeral=True)


    async def handle_post_selector(self, interaction: discord.Interaction):
        config = self.get_config()
        if not config:
            await interaction.response.send_message("Category not found.", ephemeral=True)
            return

        # Simple channel select modal
        channel_modal = Modal(title="Select Channel")
        channel_input = TextInput(label="Channel Name or ID", placeholder="#roles or channel ID", required=True)
        channel_modal.add_item(channel_input)
        
        await interaction.response.send_modal(channel_modal)
        timed_out = await channel_modal.wait()

        if timed_out:
            return

        try:
            channel = await commands.TextChannelConverter().convert(interaction, channel_input.value)
        except commands.ChannelNotFound:
            await interaction.followup.send(f"Could not find a text channel matching '{channel_input.value}'.", ephemeral=True)
            return
        
        await interaction.followup.send("Processing...", ephemeral=True)
        feedback = await self.cog.post_selector_message(interaction, config, channel, followup=True)
        await interaction.followup.send(feedback, ephemeral=True)
        await self.cog.show_category_manager(interaction, self.category_id, self.message)

    async def handle_delete_category(self, interaction: discord.Interaction):
        config = self.get_config()
        if not config:
            await interaction.response.send_message("Category not found.", ephemeral=True)
            return

        confirm_view = ConfirmView(self.author_id)
        await interaction.response.send_message(f"Are you sure you want to delete the category '{config.name}'? This cannot be undone.", view=confirm_view, ephemeral=True)
        await confirm_view.wait()

        if confirm_view.value:
            if config.message_id and config.channel_id:
                try:
                    channel = self.guild.get_channel(int(config.channel_id))
                    if channel:
                        message = await channel.fetch_message(int(config.message_id))
                        await message.delete()
                except (discord.NotFound, discord.Forbidden, discord.HTTPException):
                    pass # Ignore if we can't delete the message
            db.delete_guild_role_category_config(str(self.guild.id), self.category_id)
            await interaction.followup.send(f"Category '{config.name}' has been deleted.", ephemeral=True)
            await self.cog.show_management_dashboard(interaction, self.message)
        else:
            await interaction.followup.send("Deletion cancelled.", ephemeral=True)


class ManagementDashboardView(View):
    """Main dashboard for managing all role selection categories."""
    def __init__(self, cog: 'RoleSelectorCog', interaction: discord.Interaction):
        super().__init__(timeout=300)
        self.cog = cog
        self.guild = interaction.guild
        self.author_id = interaction.user.id
        self.message: Optional[discord.Message] = None
        self.update_view()

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        if interaction.user.id != self.author_id:
            await interaction.response.send_message("This is not for you!", ephemeral=True)
            return False
        return True

    def get_embed(self) -> discord.Embed:
        embed = discord.Embed(
            title=f"Role Selector Management for {self.guild.name}",
            description="Select a category to manage, or use the buttons below to perform actions.",
            color=discord.Color.blurple()
        )
        configs = db.get_guild_role_category_configs(str(self.guild.id))
        if not configs:
            embed.add_field(name="No Categories Found", value="Use the buttons to create your first role category!")
        else:
            for config in configs:
                role_count = len(config.roles)
                posted = "Yes" if config.message_id else "No"
                preset_info = f" (Preset: {config.preset_id})" if config.is_preset else ""
                embed.add_field(
                    name=f"{config.name}{preset_info}",
                    value=f"ID: `{config.category_id}`\nRoles: {role_count} | Posted: {posted}",
                    inline=True
                )
        embed.set_footer(text="This dashboard will time out after 5 minutes of inactivity.")
        return embed

    def update_view(self):
        self.clear_items()
        configs = db.get_guild_role_category_configs(str(self.guild.id))
        if configs:
            options = [
                discord.SelectOption(label=c.name, value=c.category_id, description=f"{len(c.roles)} roles")
                for c in configs
            ]
            self.add_item(Select(placeholder="Manage an existing category...", options=options, custom_id="manage_category_select"))

        self.add_item(Button(label="Create New Category", style=discord.ButtonStyle.success, custom_id="create_category"))
        self.add_item(Button(label="Add from Preset", style=discord.ButtonStyle.secondary, custom_id="add_from_preset"))
        self.add_item(Button(label="Manage Custom Colors", style=discord.ButtonStyle.primary, custom_id="manage_colors"))
        self.add_item(Button(label="Post/Update All", style=discord.ButtonStyle.primary, custom_id="post_all"))
        self.add_item(Button(label="Close", style=discord.ButtonStyle.danger, custom_id="close_dashboard"))

        for item in self.children:
            if hasattr(item, 'custom_id'):
                item.callback = self.dispatch_callback

    async def dispatch_callback(self, interaction: discord.Interaction):
        custom_id = interaction.data.get('custom_id')
        handler = getattr(self, f"handle_{custom_id}", None)
        if handler:
            await handler(interaction)
        else:
            await interaction.response.defer()

    async def handle_manage_category_select(self, interaction: discord.Interaction):
        category_id = interaction.data['values'][0]
        await self.cog.show_category_manager(interaction, category_id, self.message)

    async def handle_create_category(self, interaction: discord.Interaction):
        modal = CategoryModal(title="Create New Role Category")
        await interaction.response.send_modal(modal)
        timed_out = await modal.wait()

        if not timed_out:
            try:
                max_selectable = int(modal.max_selectable.value)
                if not (1 <= max_selectable <= 25):
                    raise ValueError("Max selectable must be between 1 and 25.")
            except ValueError as e:
                await interaction.followup.send(f"Invalid input: {e}", ephemeral=True)
                return

            new_config = GuildRoleCategoryConfig(
                guild_id=str(self.guild.id),
                name=modal.name.value,
                description=modal.description.value,
                roles=[],
                max_selectable=max_selectable,
            )
            db.save_guild_role_category_config(new_config)
            await interaction.followup.send(f"Category '{new_config.name}' created. You can now manage it to add roles.", ephemeral=True)
            await self.cog.show_management_dashboard(interaction, self.message)

    async def handle_add_from_preset(self, interaction: discord.Interaction):
        presets = db.get_all_role_category_presets()
        if not presets:
            await interaction.response.send_message("No global presets are available.", ephemeral=True)
            return

        options = [discord.SelectOption(label=p.name, value=p.id, description=p.description[:100]) for p in presets]
        
        class PresetSelectView(View):
            def __init__(self, author_id):
                super().__init__(timeout=180)
                self.author_id = author_id
                self.selected_preset_id = None
                select = Select(placeholder="Choose a preset to add...", options=options)
                select.callback = self.select_callback
                self.add_item(select)
            
            async def select_callback(self, inter: discord.Interaction):
                self.selected_preset_id = inter.data['values'][0]
                self.stop()
                await inter.response.defer()

            async def interaction_check(self, inter: discord.Interaction) -> bool:
                if inter.user.id != self.author_id:
                    await inter.response.send_message("This is not for you.", ephemeral=True)
                    return False
                return True

        preset_view = PresetSelectView(self.author_id)
        original_response = await interaction.response.send_message("Select a preset to add to your server.", view=preset_view, ephemeral=True)
        await preset_view.wait()
        
        if preset_view.selected_preset_id:
            await interaction.edit_original_response(content="Adding preset... please wait.", view=None)
            feedback = await self.cog.add_preset_to_guild(interaction, preset_view.selected_preset_id)
            await interaction.followup.send(feedback, ephemeral=True)
            await self.cog.show_management_dashboard(interaction, self.message)
        else:
            await interaction.edit_original_response(content="Preset selection cancelled or timed out.", view=None)

    async def handle_manage_colors(self, interaction: discord.Interaction):
        # Simple view to post the color button
        view = View(timeout=180)
        button = Button(label="Post Custom Color UI", style=discord.ButtonStyle.blurple)
        
        async def button_callback(inter: discord.Interaction):
            channel_modal = Modal(title="Select Channel")
            channel_input = TextInput(label="Channel Name or ID", placeholder="#roles or channel ID", required=True)
            channel_modal.add_item(channel_input)
            await inter.response.send_modal(channel_modal)
            timed_out = await channel_modal.wait()
            if timed_out: return

            try:
                channel = await commands.TextChannelConverter().convert(inter, channel_input.value)
                embed = discord.Embed(
                    title="🎨 Custom Role Color",
                    description="Click the button below to set a custom color for your name in this server!",
                    color=discord.Color.random(),
                )
                await channel.send(embed=embed, view=CustomColorButtonView())
                await inter.followup.send(f"Custom color button posted in {channel.mention}.", ephemeral=True)
            except commands.ChannelNotFound:
                await inter.followup.send("Channel not found.", ephemeral=True)
            except discord.Forbidden:
                await inter.followup.send("I don't have permission to send messages in that channel.", ephemeral=True)

        button.callback = button_callback
        view.add_item(button)
        await interaction.response.send_message("Use the button below to post the UI for setting custom role colors.", view=view, ephemeral=True)

    async def handle_post_all(self, interaction: discord.Interaction):
        all_configs = db.get_guild_role_category_configs(str(interaction.guild.id))
        if not all_configs:
            await interaction.response.send_message("No categories configured for this server.", ephemeral=True)
            return
        
        channel_modal = Modal(title="Select Channel For All Selectors")
        channel_input = TextInput(label="Channel Name or ID", placeholder="#roles or channel ID", required=True)
        channel_modal.add_item(channel_input)
        await interaction.response.send_modal(channel_modal)
        timed_out = await channel_modal.wait()
        if timed_out: return

        try:
            channel = await commands.TextChannelConverter().convert(interaction, channel_input.value)
        except commands.ChannelNotFound:
            await interaction.followup.send(f"Could not find a text channel matching '{channel_input.value}'.", ephemeral=True)
            return

        await interaction.followup.send(f"Posting all selectors to {channel.mention}...", ephemeral=True)
        feedback = []
        for config in all_configs:
            result = await self.cog.post_selector_message(interaction, config, channel, followup=True)
            feedback.append(result)
        
        await interaction.followup.send("Finished posting all selectors:\n- " + "\n- ".join(feedback), ephemeral=True)

    async def handle_close_dashboard(self, interaction: discord.Interaction):
        await interaction.message.delete()
        self.stop()


class RefactoredRoleSelectorCog(commands.Cog, name="RoleSelector"):
    roleselect_group = app_commands.Group(
        name="roleselect", description="Manage role selection categories and selectors."
    )
    rolepreset_group = app_commands.Group(
        name="rolepreset", description="Manage global role category presets."
    )

    def __init__(self, bot):
        self.bot = bot
        self.bot.loop.create_task(self.register_all_persistent_views())

    async def register_all_persistent_views(self):
        await self.bot.wait_until_ready()
        print("RoleSelectorCog: Registering persistent views...")
        registered_count = 0

        # Register RoleSelectorView for each guild config
        guild_configs_data = db.get_all_guild_role_category_configs()
        for guild_id_str, category_configs_list in guild_configs_data.items():
            guild_id = int(guild_id_str)
            guild = self.bot.get_guild(guild_id)
            if not guild:
                print(f"  Skipping guild {guild_id} (not found).")
                continue
            for category_config in category_configs_list:
                if category_config.roles:  # Only register if there are roles
                    try:
                        view = RoleSelectorView(guild_id, category_config, self.bot)
                        self.bot.add_view(view)
                        registered_count += 1
                    except Exception as e:
                        print(
                            f"  Error registering RoleSelectorView for {category_config.name} in {guild.id}: {e}"
                        )

        # Register CustomColorButtonView (it's globally persistent by its custom_id)
        try:
            self.bot.add_view(CustomColorButtonView())
            print("  Registered CustomColorButtonView globally.")
            registered_count += 1
        except Exception as e:
            print(f"  Error registering CustomColorButtonView globally: {e}")

        print(
            f"RoleSelectorCog: Finished registering {registered_count} persistent views."
        )

    def _get_guild_category_config(self, guild_id: int, category_name_or_id: str) -> Optional[GuildRoleCategoryConfig]:
        configs = db.get_guild_role_category_configs(str(guild_id))
        for config in configs:
            if config.category_id == category_name_or_id or config.name.lower() == category_name_or_id.lower():
                return config
        return None

    # --- New Management Command ---
    @roleselect_group.command(name="manage", description="Open the interactive role selector management dashboard.")
    @app_commands.checks.has_permissions(manage_guild=True)
    async def roleselect_manage(self, interaction: discord.Interaction):
        await self.show_management_dashboard(interaction)

    # --- UI-driving Helper Methods ---
    async def show_management_dashboard(self, interaction: discord.Interaction, message: Optional[discord.Message] = None):
        view = ManagementDashboardView(self, interaction)
        embed = view.get_embed()
        if message:
            await message.edit(embed=embed, view=view)
        else:
            await interaction.response.send_message(embed=embed, view=view, ephemeral=True)
        view.message = await interaction.original_response()

    async def show_category_manager(self, interaction: discord.Interaction, category_id: str, message: Optional[discord.Message] = None):
        view = CategoryManagementView(self, interaction, category_id)
        config = view.get_config()
        if not config:
            await interaction.response.send_message("This category no longer exists.", ephemeral=True)
            return await self.show_management_dashboard(interaction, message)

        embed = discord.Embed(
            title=f"Managing: {config.name}",
            description=config.description or "No description provided.",
            color=discord.Color.green()
        )
        embed.add_field(name="Category ID", value=f"`{config.category_id}`", inline=True)
        embed.add_field(name="Max Selectable", value=str(config.max_selectable), inline=True)
        embed.add_field(name="Is Preset", value="Yes" if config.is_preset else "No", inline=True)
        
        roles_str = "\n".join([f"{r.emoji or '•'} {r.name}" for r in config.roles]) or "No roles in this category yet."
        embed.add_field(name="Roles", value=roles_str, inline=False)

        if message:
            await message.edit(embed=embed, view=view)
        else:
            # This case happens when switching from dashboard to manager
            await interaction.response.edit_message(embed=embed, view=view)
        view.message = await interaction.original_response()

    # --- UI-driving Helper Methods (Continued) ---
    async def post_selector_message(self, interaction: discord.Interaction, config: GuildRoleCategoryConfig, target_channel: discord.TextChannel, followup: bool = False) -> str:
        """Helper to post or edit a role selector message."""
        if not config.roles:
            return f"Skipped '{config.name}': No roles configured."

        embed = discord.Embed(
            title=f"✨ {config.name} Roles ✨",
            description=config.description,
            color=discord.Color.blue(),
        )
        view = RoleSelectorView(interaction.guild.id, config, self.bot)

        try:
            if config.message_id and config.channel_id:
                try:
                    original_channel = self.bot.get_channel(int(config.channel_id))
                    if isinstance(original_channel, discord.TextChannel):
                        message_to_edit = await original_channel.fetch_message(int(config.message_id))
                        if original_channel.id != target_channel.id:
                            await message_to_edit.delete()
                            raise discord.NotFound("Message moved.")
                        await message_to_edit.edit(embed=embed, view=view)
                        return f"Updated selector for '{config.name}' in {target_channel.mention}."
                except (discord.NotFound, discord.Forbidden):
                    pass
            
            msg = await target_channel.send(embed=embed, view=view)
            config.message_id = str(msg.id)
            config.channel_id = str(target_channel.id)
            db.save_guild_role_category_config(config)
            return f"Posted new selector for '{config.name}' in {target_channel.mention}."
        except discord.Forbidden:
            return f"Error posting '{config.name}': I lack permissions in {target_channel.mention}."
        except Exception as e:
            return f"Error posting '{config.name}': {e}"

    async def add_preset_to_guild(self, interaction: discord.Interaction, preset_id: str) -> str:
        if not interaction.guild.me.guild_permissions.manage_roles:
            return "I need 'Manage Roles' permission to create roles from presets."

        preset = db.get_role_category_preset(preset_id)
        if not preset:
            return f"Preset with ID '{preset_id}' not found."

        if self._get_guild_category_config(interaction.guild.id, preset.name):
            return f"A category based on preset '{preset.name}' already exists."

        color_map = {
            "Red": discord.Color.red(), "Blue": discord.Color.blue(), "Green": discord.Color.green(),
            "Yellow": discord.Color.gold(), "Purple": discord.Color.purple(), "Orange": discord.Color.orange(),
            "Pink": discord.Color.fuchsia(), "Black": discord.Color(0x010101), "White": discord.Color(0xFEFEFE),
        }

        roles_to_add: List[GuildRole] = []
        created_feedback = []

        for prole in preset.roles:
            existing_role = discord.utils.get(interaction.guild.roles, name=prole.name)
            if existing_role:
                roles_to_add.append(GuildRole(role_id=str(existing_role.id), name=existing_role.name, emoji=prole.emoji))
            else:
                color = color_map.get(prole.name, discord.Color.default())
                try:
                    new_role = await interaction.guild.create_role(
                        name=prole.name, color=color, permissions=discord.Permissions.none(),
                        reason=f"Auto-created for preset '{preset.name}'"
                    )
                    roles_to_add.append(GuildRole(role_id=str(new_role.id), name=new_role.name, emoji=prole.emoji))
                    created_feedback.append(f"Created role '{new_role.name}'")
                except discord.Forbidden:
                    return f"Aborted: I lack permission to create the role '{prole.name}'."
                except discord.HTTPException as e:
                    return f"Aborted: Failed to create role '{prole.name}': {e}."

        new_config = GuildRoleCategoryConfig(
            guild_id=str(interaction.guild.id), name=preset.name, description=preset.description,
            roles=roles_to_add, max_selectable=preset.max_selectable, is_preset=True, preset_id=preset.id,
        )
        db.save_guild_role_category_config(new_config)
        
        feedback = f"Category '{preset.name}' added."
        if created_feedback:
            feedback += "\n" + "\n".join(created_feedback)
        return feedback

    # --- Kept Commands for Utility ---
    @roleselect_group.command(name="listpresets", description="Lists all available global role category presets.")
    async def roleselect_listpresets(self, interaction: discord.Interaction):
        # This command's implementation is unchanged from the original file
        presets = db.get_all_role_category_presets()
        if not presets:
            await interaction.response.send_message("No global presets available.", ephemeral=True)
            return
        embed = discord.Embed(title="Available Role Category Presets", color=discord.Color.green())
        for preset in sorted(presets, key=lambda p: p.display_order):
            roles_str = ", ".join([f"{r.name}" for r in preset.roles[:5]]) + ("..." if len(preset.roles) > 5 else "")
            embed.add_field(
                name=f"{preset.name} (ID: `{preset.id}`)",
                value=f"Desc: {preset.description}\nMax: {preset.max_selectable}\nRoles: {roles_str or 'None'}",
                inline=False,
            )
        await interaction.response.send_message(embed=embed, ephemeral=True)

    # --- All Owner-Only Preset Commands are kept as-is ---
    @rolepreset_group.command(
        name="add", description="Creates a new global role category preset."
    )
    @app_commands.check(is_owner_check)
    @app_commands.describe(
        preset_id="A unique ID for this preset (e.g., 'color_roles', 'region_roles').",
        name="The display name for this preset.",
        description="A description for this preset.",
        max_selectable="Maximum roles a user can select from categories using this preset (default: 1).",
        display_order="Order in which this preset appears in lists (lower numbers first, default: 0).",
    )
    async def rolepreset_add(self, interaction: discord.Interaction, preset_id: str, name: str, description: str, max_selectable: int = 1, display_order: int = 0):
        if db.get_role_category_preset(preset_id):
            await interaction.response.send_message(f"Preset ID '{preset_id}' already exists.", ephemeral=True)
            return
        new_preset = RoleCategoryPreset(id=preset_id, name=name, description=description, roles=[], max_selectable=max_selectable, display_order=display_order)
        db.save_role_category_preset(new_preset)
        await interaction.response.send_message(f"Preset '{name}' created.", ephemeral=True)

    @rolepreset_group.command(
        name="remove", description="Removes a global role category preset."
    )
    @app_commands.check(is_owner_check)
    @app_commands.describe(preset_id="The ID of the preset to remove.")
    async def rolepreset_remove(self, interaction: discord.Interaction, preset_id: str):
        if not db.get_role_category_preset(preset_id):
            await interaction.response.send_message(
                f"Preset ID '{preset_id}' not found.", ephemeral=True
            )
            return
        db.delete_role_category_preset(preset_id)
        await interaction.response.send_message(
            f"Preset ID '{preset_id}' removed.", ephemeral=True
        )

    @rolepreset_group.command(name="addrole", description="Adds a role to a global preset.")
    @app_commands.check(is_owner_check)
    async def rolepreset_addrole(self, interaction: discord.Interaction, preset_id: str, role_name_or_id: str, emoji: Optional[str] = None):
        preset = db.get_role_category_preset(preset_id)
        if not preset:
            await interaction.response.send_message(f"Preset ID '{preset_id}' not found.", ephemeral=True)
            return
        
        # For presets, we just store the name, not a guild-specific ID
        if any(r.name.lower() == role_name_or_id.lower() for r in preset.roles):
             await interaction.response.send_message(f"Role '{role_name_or_id}' is already in preset '{preset.name}'.", ephemeral=True)
             return

        preset.roles.append(RoleOption(role_id="0", name=role_name_or_id, emoji=emoji)) # role_id is a placeholder
        db.save_role_category_preset(preset)
        await interaction.response.send_message(f"Role '{role_name_or_id}' added to preset '{preset.name}'.", ephemeral=True)

    @rolepreset_group.command(name="removerole", description="Removes a role from a global preset.")
    @app_commands.check(is_owner_check)
    async def rolepreset_removerole(self, interaction: discord.Interaction, preset_id: str, role_id_or_name: str):
        preset = db.get_role_category_preset(preset_id)
        if not preset:
            await interaction.response.send_message(f"Preset ID '{preset_id}' not found.", ephemeral=True)
            return

        initial_len = len(preset.roles)
        preset.roles = [r for r in preset.roles if r.name.lower() != role_id_or_name.lower()]

        if len(preset.roles) < initial_len:
            db.save_role_category_preset(preset)
            await interaction.response.send_message(f"Role '{role_id_or_name}' removed from preset '{preset.name}'.", ephemeral=True)
        else:
            await interaction.response.send_message(f"Role '{role_id_or_name}' not found in preset '{preset.name}'.", ephemeral=True)


async def setup(bot):
    # We need to make sure the original cog isn't loaded if this one is.
    # For now, we assume this file will replace the old one.
    cog = RefactoredRoleSelectorCog(bot)
    await bot.add_cog(cog)
    print("RefactoredRoleSelectorCog loaded.")