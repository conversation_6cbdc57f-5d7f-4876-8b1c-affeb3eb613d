# Discord Bot Token
DISCORD_TOKEN=YOUR_DISCORD_BOT_TOKEN_HERE

# OpenRouter API Key (Required for GurtCog AI features)
AI_API_KEY=YOUR_OPENROUTER_API_KEY_HERE

# Tavily API Key (Optional, for GurtCog web search tool)
TAVILY_API_KEY=YOUR_TAVILY_API_KEY_HERE

# GurtCog Configuration (Optional, defaults will be used if not set)
OPENROUTER_API_URL=https://openrouter.ai/api/v1/chat/completions
GURT_DEFAULT_MODEL=google/gemini-2.5-flash-preview:thinking
GURT_FALLBACK_MODEL=openai/gpt-4.1-nano
GURT_DB_PATH=data/gurt_memory.db
GURT_CHROMA_PATH=data/chroma_db # Optional - Path to the ChromaDB persistent storage directory
GURT_SEMANTIC_MODEL=all-MiniLM-L6-v2 # Optional - Sentence Transformer model for semantic search

# Google Cloud / Vertex AI Configuration (Required for Google GenAI features)
GCP_PROJECT_ID=your_gcp_project_id
GCP_LOCATION=us-central1
# GOOGLE_APPLICATION_CREDENTIALS=/path/to/your/service-account-key.json

# GurtCog Proactive Engagement Configuration (Optional)
PROACTIVE_LULL_THRESHOLD=180 # Seconds of inactivity before considering a lull (Default: 180)
PROACTIVE_BOT_SILENCE_THRESHOLD=600 # Seconds bot must be silent before lull trigger (Default: 600)
PROACTIVE_LULL_CHANCE=0.3 # Chance (0.0-1.0) to respond on lull trigger (Default: 0.3)
PROACTIVE_TOPIC_RELEVANCE_THRESHOLD=0.6 # Minimum semantic similarity score for topic trigger (Default: 0.6)
PROACTIVE_TOPIC_CHANCE=0.4 # Chance (0.0-1.0) to respond on topic trigger (Default: 0.4)
PROACTIVE_RELATIONSHIP_SCORE_THRESHOLD=70 # Minimum relationship score (0-100) for relationship trigger (Default: 70)
PROACTIVE_RELATIONSHIP_CHANCE=0.2 # Chance (0.0-1.0) to respond on relationship trigger (Default: 0.2)

# Discord OAuth2 Configuration (For OAuth Cog)
DISCORD_CLIENT_ID=YOUR_DISCORD_APP_CLIENT_ID
DISCORD_CLIENT_SECRET=YOUR_DISCORD_APP_CLIENT_SECRET
DISCORD_REDIRECT_URI=YOUR_DISCORD_APP_REDIRECT_URI
FLASK_SECRET_KEY=YOUR_FLASK_SECRET_KEY_HERE # Used for session management in Flask server

# API Server Base URL (For Discord Sync Cog)
API_BASE_URL=http://127.0.0.1:5001 # Example URL for the API server

# Unified API Configuration (For run_unified_api.py)
API_HOST=0.0.0.0
API_PORT=8001
# Add other necessary environment variables for your specific setup

# Piston API Configuration (For GurtCog run_python_code tool)
PISTON_API_URL=https://emkc.org/api/v2/piston/execute # Example public Piston instance URL
# PISTON_API_KEY=YOUR_PISTON_API_KEY_IF_NEEDED # Optional, depending on the Piston instance used

# PostgreSQL Configuration (If using PostgreSQL for features like economy, settings, etc.)
# POSTGRES_HOST=localhost
# POSTGRES_PORT=5432
# POSTGRES_USER=your_postgres_user
# POSTGRES_PASSWORD=your_postgres_password
# POSTGRES_DB=your_primary_database_name # e.g., bot-economy
# POSTGRES_SETTINGS_DB=your_settings_database_name # e.g., discord_bot_settings

# Redis Configuration (If using Redis for caching, etc.)
# REDIS_HOST=localhost
# REDIS_PORT=6379
# REDIS_PASSWORD=your_redis_password # Optional

# Terminal Command Execution Configuration (For GurtCog run_terminal_command tool)
GURT_SAFETY_CHECK_MODEL=openai/gpt-4.1-nano # Model for AI safety check (e.g., openai/gpt-4.1-nano)
DOCKER_EXEC_IMAGE=alpine:latest # Docker image for command execution (e.g., alpine:latest)
DOCKER_COMMAND_TIMEOUT=10 # Timeout in seconds for container execution (e.g., 10)
DOCKER_CPU_LIMIT=0.5 # CPU cores limit for container (e.g., 0.5)
DOCKER_MEM_LIMIT=64m # Memory limit for container (e.g., 64m)
